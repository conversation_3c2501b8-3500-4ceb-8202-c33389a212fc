name: sfpApps SmokeTest
on:
  workflow_dispatch:
    inputs:
      env:
        description: "env"
        required: true
        default: stage
        type: choice
        options:
          - dev
          - test
          - qa1
          - qa2
          - stage

concurrency:
  group: deploy-kr-SmokeTest
  cancel-in-progress: true

env:
  DEFAULT_ENV: stage
  ARTIFACTORY_EDGE_USERNAME_REF: ${{secrets.ARTIFACTORY_EDGE_USERNAME}}
  ARTIFACTORY_EDGE_TOKEN_REF: ${{secrets.ARTIFACTORY_EDGE_TOKEN}}

jobs:
  build:
    runs-on: aks
    container: maven:3.9.8-eclipse-temurin-11
    steps:
      - name: Checkout Smoke Code
        uses: actions/checkout@v4


      - name: Run Smoke Tests
        run: |
          echo "Environment: ${{ github.event.inputs.env || env.DEFAULT_ENV }}"
          mvn clean test --settings settings.xml -Dtest=SmokeTestRunner -Dkarate.env=${{ github.event.inputs.env || env.DEFAULT_ENV }} -Dclient_id=${{ secrets.CLIENT_ID }} -Dclient_secret=${{ secrets.CLIENT_SECRET }} -Dkafka_pwd=${{ secrets.KAFKA_PWD }}

      - name: Smoke QMetry
        if: always()
        run: >-
          mvn exec:java --settings settings.xml -DsuiteName="Event manager karate Smoke Tests"

      - name: Upload Smoke artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: Karate Cucumber Smoke Reports
          path: ./target/cucumber-html-reports

      - name:  Publish SmokeTest Report
        uses: mikepenz/action-junit-report@v3 # Generate "Tests env summary"
        if: success() || failure() # always run even if the previous step fails
        with:
          report_paths: "**/target/karate-reports/*.xml"
          detailed_summary: true

      - name: Upload Smoke results to Qmetry
        if: success() || failure()
        run: >-
          mvn exec:java --settings settings.xml -DsuiteName=Karate_Assortment_Item -Denviroment="${{ github.event.inputs.env || env.DEFAULT_ENV }}"

      - name: Upload Smoke API artifacts
        if: success() || failure()
        uses: actions/upload-artifact@v4
        with:
          name: Karate Cucumber Smoke Reports
          path: ./karate-api-testing/target/cucumber-html-reports