name: sfpApps RegressionTest
on:
  workflow_dispatch:
    inputs:
      env:
        description: "env"
        required: true
        default: stage
        type: choice
        options:
          - dev
          - test
          - qa1
          - qa2
          - stage
  
concurrency:
  group: deploy-kr-RegressionTest
  cancel-in-progress: true

env:
  DEFAULT_ENV: stage
  ARTIFACTORY_EDGE_USERNAME_REF: ${{secrets.ARTIFACTORY_EDGE_USERNAME}}
  ARTIFACTORY_EDGE_TOKEN_REF: ${{secrets.ARTIFACTORY_EDGE_TOKEN}}

jobs:
  build:
    runs-on: aks
    container: maven:3.9.8-eclipse-temurin-11
    steps:
      - name: Checkout Regression Code
        uses: actions/checkout@v4


      - name: Run Regression Tests
        run: |
          echo "Environment: ${{ github.event.inputs.env || env.DEFAULT_ENV }}"
          mvn clean test --settings settings.xml -Dtest=RegressionTestRunner -Dkarate.env=${{ github.event.inputs.env || env.DEFAULT_ENV }} -Dclient_id=${{ secrets.CLIENT_ID }} -Dclient_secret=${{ secrets.CLIENT_SECRET }} -Dkafka_pwd=${{ secrets.KAFKA_PWD }}

      - name: Regression QMetry
        if: always()
        run: >-
          mvn exec:java --settings settings.xml -DsuiteName="Event manager karate Regression Tests"

      - name: Upload Regression artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: Karate Cucumber Regression Reports
          path: ./target/cucumber-html-reports

      - name: Publish Regressionn Test Report
        uses: mikepenz/action-junit-report@v3 # Generate "Tests env summary"
        if: success() || failure() # always run even if the previous step fails
        with:
          report_paths: "**/target/karate-reports/*.xml"
          detailed_summary: true

      - name: Upload Regression results to Qmetry
        if: success() || failure()
        run: >-
          mvn exec:java --settings settings.xml -DsuiteName=Karate_Assortment_Item -Denviroment="${{ github.event.inputs.env || env.DEFAULT_ENV }}"

      - name: Upload Regresssion API artifacts
        if: success() || failure()
        uses: actions/upload-artifact@v4
        with:
          name: Karate Cucumber Regression Reports
          path: ./karate-api-testing/target/cucumber-html-reports