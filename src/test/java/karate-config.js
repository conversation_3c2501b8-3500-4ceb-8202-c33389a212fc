function karateConfig() {
    let env = karate.env; // get system property 'karate.env'
    karate.log('karate.env system property was:', env);

    if (!env) {
        env = 'local';
    }

    // Get Kafka password from system properties, environment variable, or fallback to hardcoded value
    let config = {
        env: env,
        // Kafka configuration
        kaUser: 's5940despn',
        kafkaTopic: 'sfp-execution-event',
        kafkaPropertiesPath: 'classpath:plain.properties',
        kafkaTempFilePath: '/tmp/plain_tmp_kafka.properties',
        kafkaWaitTime: 20000, // 20 seconds default wait time for Kafka messages

        // API endpoints
        // em_base_url is DS1 base utl
        // em_base_em_url is EM endpoint base url
        db_service_url: 'https://apd-db-service-test.kpsazc.dgtl.kroger.com',
        em_base_url: 'localhost:8080',
        em_base_em_url: 'localhost:8081',
        nav_base_nav_url: 'localhost:8082',
        em_kafka_base_url_root: 'https://sfpapps-event-manager-kafka-stage.itmloc.rch-cdc-cxnonprod.kroger.com',

        // API paths
        refreshEventPath: '/api/v1/sfp-apps/event-manager-kafka/publish/refresh-event',
        areaEventPath: '/api/v1/sfp-apps/event-manager-kafka/publish/area-event',
        complianceEventPath: '/api/v1/sfp-apps/event-manager-kafka/publish/compliancy-event',
        sgm_URL: `https://assortment-item.sfdomn-dev.kpsazc.dgtl.kroger.com`,
        // Other configuration
        apiKey: 'Basic c3ZjODM3OGFyZWFwaXM6aGowUnFHOHZvM0xIRDFBS01hdkNoSQ==',
        testUtil_classpath: 'com.krogerqa.sfpAppsKarate.globalUtils.TestUtil',

        // Common test data
        defaultTimeStr: "Thu May 14 16:50:00 UTC 2025"
    };

    // Instantiate utils globally
    config.utils = Java.type(config.testUtil_classpath);
    config.TestUtil = Java.type('com.krogerqa.sfpAppsKarate.globalUtils.TestUtil');

    // Helper functions
    config.sleep = function(millis) {
        java.lang.Thread.sleep(millis);
    };

    // Set timeouts
    karate.configure('connectTimeout', 200000);
    karate.configure('readTimeout', 200000);

    if (env === 'dev') {
        config.em_base_url = 'https://assortment-item.sfdomn-dev.kpsazc.dgtl.kroger.com'
        config.em_kafka_base_url_root = 'https://sfpapps-event-manager-kafka-dev.itmloc.rch-cdc-cxnonprod.kroger.com';
        config.em_base_em_url = 'https://sfp-apps-em-dev.itmloc.rch-cdc-cxnonprod.kroger.com';
        config.nav_base_nav_url = 'https://sfp-apps-nav-dev.itmloc.rch-cdc-cxnonprod.kroger.com';
    }

    if (env === 'test') {
        //defaullt to dev
        config.em_base_url = 'https://assortment-item.sfdomn-dev.kpsazc.dgtl.kroger.com'
        //default to stage
        config.em_kafka_base_url_root = 'https://sfpapps-event-manager-kafka-stage.itmloc.rch-cdc-cxnonprod.kroger.com';
        config.em_base_em_url = 'https://sfp-apps-em-test.itmloc.rch-cdc-cxnonprod.kroger.com';
        config.nav_base_nav_url = 'https://sfp-apps-nav-test.itmloc.rch-cdc-cxnonprod.kroger.com';
    }

    if (env === 'qa1') {
        config.em_base_url = 'https://assortment-item.sfdomn-qa1.kpsazc.dgtl.kroger.com'
        //default to test
        config.em_kafka_base_url_root = 'https://sfpapps-event-manager-kafka-test.itmloc.rch-cdc-cxnonprod.kroger.com';
        config.em_base_em_url = 'https://sfp-apps-em-qa1.itmloc.rch-cdc-cxnonprod.kroger.com';
        config.nav_base_nav_url = 'https://sfp-apps-nav-qa1-stage.itmloc.rch-cdc-cxnonprod.kroger.com';
    }

    if (env === 'qa2') {
        //default qa2 to qa1 for ds
        config.em_base_url = 'https://assortment-item.sfdomn-qa1.kpsazc.dgtl.kroger.com'
        //default to test
        config.em_kafka_base_url_root = 'https://sfpapps-event-manager-kafka-test.itmloc.rch-cdc-cxnonprod.kroger.com';
        config.em_base_em_url = 'https://sfp-apps-em-qa2.itmloc.rch-cdc-cxnonprod.kroger.com';
        config.nav_base_nav_url = 'https://sfp-apps-nav-qa2-stage.itmloc.rch-cdc-cxnonprod.kroger.com';
    }

    if (env === 'stage') {
        config.em_base_url = 'https://assortment-item.sfdomn-stage.kpsazc.dgtl.kroger.com'
        config.em_kafka_base_url_root = 'https://sfpapps-event-manager-kafka-stage.itmloc.rch-cdc-cxnonprod.kroger.com';
        config.em_base_em_url = 'https://sfp-apps-em-stage.itmloc.rch-cdc-cxnonprod.kroger.com';
        config.nav_base_nav_url = 'https://sfp-apps-nav-stage.itmloc.rch-cdc-cxnonprod.kroger.com';
    }


    return config;
}
