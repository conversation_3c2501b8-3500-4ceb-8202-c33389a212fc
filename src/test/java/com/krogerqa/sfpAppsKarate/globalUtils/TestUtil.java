package com.krogerqa.sfpAppsKarate.globalUtils;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Random;

public class TestUtil {

  /**
   * Generates a random alphanumeric string of the specified length
   * @param stringLength The length of the string to generate
   * @return A random alphanumeric string
   */
  public static String generateRandomString(int stringLength) {
    String alphaNumeric = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    Random random = new Random();
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < stringLength; i++) {
      int index = random.nextInt(alphaNumeric.length());
      sb.append(alphaNumeric.charAt(index));
    }
    return sb.toString();
  }

  /**
   * Generates a random 6-digit numeric string (e.g., "995801")
   * @return A random 6-digit numeric string
   */
  public static String generate6DigitNumericString() {
    Random random = new Random();
    // Generate a random number between 100000 and 999999 (6 digits)
    int randomNumber = 100000 + random.nextInt(900000);
    return String.valueOf(randomNumber);
  }

  public static StringBuilder createStringBuilder() {
    return new StringBuilder();
  }

  public static StringBuilder append(StringBuilder sb, String text) {
    return sb.append(text);
  }

  public static String toString(StringBuilder sb) {
    return sb.toString();
  }

  public static String convByToString(byte[] byteArr) {
    String str = new String(byteArr,StandardCharsets.UTF_8);
    return str;
  }

  public static byte[] convStringtoBy(String str) {
    byte[] byteArr = str.getBytes(StandardCharsets.UTF_8);
    return byteArr;
  }

  public static void writeKarateVariableToFile(String filePath, String variableValue) {
    try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
      writer.write(variableValue);
    } catch (IOException e) {
      System.err.println("An error occurred writing to the file: " + e.getMessage());
    }
  }

  /**
   * Generates Kafka properties with credentials and writes them to a file
   * @param basePropertiesContent The base properties content as a byte array
   * @param username The Kafka username
   * @param outputFilePath The path to write the properties file to
   * @return The path to the generated properties file
   */
  public static String generateKafkaPropertiesFile(byte[] basePropertiesContent, String username, String outputFilePath) {
    String baseContent = convByToString(basePropertiesContent);
    String password = System.getProperty("kafka_pwd");

    // Fix SSL truststore path to use absolute path
    String currentDir = System.getProperty("user.dir");
    String truststorePath = currentDir + "/src/test/resources/kafka.client.truststore.jks";

    // Replace relative truststore path with absolute path
    baseContent = baseContent.replace("ssl.truststore.location=src/test/resources/kafka.client.truststore.jks",
                                     "ssl.truststore.location=" + truststorePath);

    StringBuilder sb = createStringBuilder();
    append(sb, baseContent);
    append(sb, "\nsasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username=\"");
    append(sb, username);
    append(sb, "\" password=\"");
    append(sb, password);
    append(sb, "\";\n");
    String result = toString(sb);
    writeKarateVariableToFile(outputFilePath, result);
    return outputFilePath;
  }

  /**
   * Creates a Kafka consumer for the specified topic using the provided properties file
   * @param topic The Kafka topic to consume from
   * @param propertiesFilePath The path to the Kafka properties file
   * @return A KafkaConsumerClient instance
   */
  public static Object createKafkaConsumer(String topic, String propertiesFilePath) {
    try {
      // Set system properties to bypass SSL verification
      System.setProperty("javax.net.ssl.trustStore", System.getProperty("user.dir") + "/src/test/resources/kafka.client.truststore.jks");
      System.setProperty("javax.net.ssl.trustStorePassword", "changeit");
      System.setProperty("javax.net.ssl.trustStoreType", "JKS");

      // Disable SSL hostname verification
      System.setProperty("com.sun.net.ssl.checkRevocation", "false");
      System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");
      System.setProperty("sun.security.ssl.allowLegacyHelloMessages", "true");

      Class<?> kafkaConsumerClientClass = Class.forName("com.krogerqa.kafka.KafkaConsumerClient");
      return kafkaConsumerClientClass.getConstructor(String.class, String.class)
          .newInstance(topic, propertiesFilePath);
    } catch (Exception e) {
      System.err.println("Error creating Kafka consumer: " + e.getMessage());
      e.printStackTrace();
      return null;
    }
  }

  /**
   * Creates a Kafka producer for the specified topic using the provided properties file
   * @param topic The Kafka topic to produce to
   * @param propertiesFilePath The path to the Kafka properties file
   * @return A KafkaProducerClient instance
   */
  public static Object createKafkaProducer(String topic, String propertiesFilePath) {
    try {
      Class<?> kafkaProducerClientClass = Class.forName("com.krogerqa.kafka.KafkaProducerClient");
      return kafkaProducerClientClass.getConstructor(String.class, String.class)
          .newInstance(topic, propertiesFilePath);
    } catch (Exception e) {
      System.err.println("Error creating Kafka producer: " + e.getMessage());
      e.printStackTrace();
      return null;
    }
  }

  /**
   * Validates if a Kafka message contains the specified value
   * @param message The Kafka message as a string
   * @param valueToFind The value to search for in the message
   * @return true if the value is found, false otherwise
   */
  public static boolean validateKafkaMessage(String message, String valueToFind) {
    return message != null && !message.isEmpty() && message.contains(valueToFind);
  }

  /**
   * Sets up a complete Kafka consumer with all necessary configurations
   * @param fileContent The content of the Kafka properties file (byte array)
   * @param kafkaUser The Kafka username
   * @param kafkaTopic The Kafka topic to consume from
   * @param kafkaTempFilePath The temporary file path for generated properties
   * @return A configured KafkaConsumerClient instance
   */
  public static Object setupKafkaConsumer(byte[] fileContent, String kafkaUser, String kafkaTopic, String kafkaTempFilePath) {
    try {
      String filePath = generateKafkaPropertiesFile(fileContent, kafkaUser, kafkaTempFilePath);
      return createKafkaConsumer(kafkaTopic, filePath);
    } catch (Exception e) {
      System.err.println("Error setting up Kafka consumer: " + e.getMessage());
      e.printStackTrace();
      return null;
    }
  }
}
