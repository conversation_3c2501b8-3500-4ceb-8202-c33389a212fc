Feature: Create, Update and Delete Custom Store Group

  Background:
    * configure ssl = true
    * configure url = sgm_URL
    * def token = call read('classpath:com/krogerqa/sfpAppsKarate/features/DS1/token.feature@authToken')
    * header Authorization = token.bearerToken
    * def utils = Java.type(testUtil_classpath)
    * def randomUUID = utils.generateRandomString(8)

  @smoke @regression @SGM
  Scenario: create, update, and validate custom store group
      # Create CSG
      * path '/assortment/storeGroup/customStoreGroup'
      * request {"category": "EM","name": '#(randomUUID)',"revBy": "DOU1440","revByName": "SRIVASTAVA, SAURABH","division": "014","stores": ["00335","00336","00344","00351"]}
      * method POST
      # Get CSG by group id
      * def groupId = response.data.groupId
      * header Authorization = token.bearerToken
      * path '/assortment/storeGroup/customStoreGroup/' + groupId
      * method GET
      Then status 200
      * def originalStores = response.data.stores
      # Update CSG
      * header Authorization = token.bearerToken
      * path '/assortment/storeGroup/customStoreGroup/' + groupId
      * request {"name": "#(randomUUID)","revBy": "userid","revByName": "user name","stores": ["00361", "00364", "00359", "00367"]}
      * method PUT
      Then status 200
      * def updatedStores = response.data.stores
      * print 'Updated stores from response:', updatedStores

      # Validate updated stores do not match original stores
      * match updatedStores != originalStores
      * print 'Store validation passed - stores were successfully updated'

      # Schema validation for updated response
      * def schema12 = read('classpath:schema/EventManagerServiceSchema/CustomStoreGroup.json')
      * match response == schema12

      # Delete CSG
      * header Authorization = token.bearerToken
      * path '/assortment/storeGroup/customStoreGroup/' + groupId
      * method DELETE
      Then status 204

      # Get the deleted CSG
      * header Authorization = token.bearerToken
      * path '/assortment/storeGroup/customStoreGroup/' + groupId
      * method GET
      Then status 404'

