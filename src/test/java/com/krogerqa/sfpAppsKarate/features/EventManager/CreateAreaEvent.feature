
Feature: Create Area Event

  Background:
    * configure ssl = true
    * configure url = em_kafka_base_url_root
    * def consumer = TestUtil.setupKafkaConsumer(read(kafkaPropertiesPath), kaUser, kafkaTopic, kafkaTempFilePath)
    
  @regression @AreaEvent @kafka
  Scenario Outline: create area event
    * print 'Using URL:', em_kafka_base_url_root + areaEventPath
    * path areaEventPath
    * def eventId = TestUtil.generate6DigitNumericString() 
    * json body = read('classpath:data/Input/ComplianceEvent/AreaEvent.json')[<index>]
    * print 'Request body from file:', body.RequestBody
    * request body.RequestBody
    * method POST
    * print 'Response status:', responseStatus
    * print 'Response type:', karate.typeOf(response)
    Then status 200

    # Wait for Kafka message to be published
    * print 'Waiting for message to be published to Kafka (' + kafkaWaitTime + ' milliseconds)...'
    * sleep(kafkaWaitTime)

    * print 'Validating message content'
    * print 'Looking for eventId:', eventId
    * def testMessages = consumer.getMessagesContainingValue(eventId)

    * def rawMessage = testMessages ? testMessages.toString() : ''
    * print 'Raw message from Kafka:', rawMessage

    * def validationResult = TestUtil.validateKafkaMessage(rawMessage, eventId)
    * print 'Message contains eventId:', validationResult
    * assert validationResult == true

    # Close the consumer
    * consumer.close()

    # Print test results
    * print 'TEST COMPLETED SUCCESSFULLY'

    Examples:
      | index |
      | 0     |