
Feature: Create Refresh Event

  Background:
    * configure ssl = true
    * configure url = em_kafka_base_url_root
    * def recordId = TestUtil.generateRandomString(10)
    * def consumer = TestUtil.setupKafkaConsumer(read(kafkaPropertiesPath), kaUser, kafkaTopic, kafkaTempFilePath)

  @regression @RefreshEvent @kafka
  Scenario Outline: create refresh event
    * print 'Using URL:', em_kafka_base_url_root + refreshEventPath
    * path refreshEventPath
    * def currentTimeStr = defaultTimeStr
    * def uniqueId = "KarateKafkaTest" + recordId
    * def eventId = TestUtil.generate6DigitNumericString()
    * json body = read('classpath:data/Input/ComplianceEvent/RefreshEvent.json')[<index>]
    * print 'Request body from file:', body.RequestBody
    * request body.RequestBody
    * method POST
    * print 'Response status:', responseStatus
    * print 'Response type:', karate.typeOf(response)
    Then status 200
    * print 'Waiting for message to be published to Kafka (' + kafkaWaitTime + ' milliseconds)...'
    * sleep(kafkaWaitTime)

    * def testMessages = consumer.getMessagesContainingValue(eventId)
    * def rawMessage = testMessages.toString()
    * print 'Raw message from Kafka:', rawMessage

    * def validationResult = TestUtil.validateKafkaMessage(rawMessage, eventId)
    * print 'Validation result:', validationResult
    * assert validationResult == true

    # Close the consumer
    * consumer.close()
    * print 'TEST COMPLETED SUCCESSFULLY'

    Examples:
      | index |
      | 0     |