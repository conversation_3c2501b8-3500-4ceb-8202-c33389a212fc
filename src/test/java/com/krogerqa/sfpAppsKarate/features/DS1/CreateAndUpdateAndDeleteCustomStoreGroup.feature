Feature: Create, Update, Get, and Delete Custom Store Group

  Background:
    * configure ssl = true
    * configure url = em_base_url
    * def token = call read('classpath:com/krogerqa/sfpAppsKarate/features/DS1/token.feature@authToken')
    * header Authorization = token.bearerToken
    * def utils = Java.type(testUtil_classpath)
    * def name = utils.generateRandomString(4)

  @regression @CSG
  Scenario: create, update, and validate custom store group
    # Create CSG
    Given path '/assortment/storeGroup/customStoreGroup'
    When request {"category": "EM","name": '#(name)',"revBy": "DOU4443","revByName": "SHARMA, VARUN","division": "014","stores": ["00335","00336","00344","00351"]}
    And method POST
    Then status 201
    # Get CSG by group id
    Given def groupId = response.data.groupId
    And header Authorization = token.bearerToken
    And path '/assortment/storeGroup/customStoreGroup/' + groupId
    When method GET
    * print 'Original CSG response:', response
    Then status 200
    * def originalStores = response.data.stores
    * print 'stores from  response:', originalStores
    # Update CSG
    Given header Authorization = token.bearerToken
    And path '/assortment/storeGroup/customStoreGroup/' + groupId
    And request {"name": "#(name)","revBy": "userid","revByName": "user name","stores": ["00361", "00364", "00359", "00367"]}
    When method PUT
    * print 'Updated CSG response:', response
    Then status 200
    * def updatedStores = response.data.stores

    # Validate updated stores do not match original stores
    * match updatedStores != originalStores

    # Schema validation for updated response
    * def schema12 = read('classpath:schema/EventManagerServiceSchema/CustomStoreGroup.json')
    * match response == schema12

    # Delete CSG
    Given header Authorization = token.bearerToken
    And path '/assortment/storeGroup/customStoreGroup/' + groupId
    When method DELETE
    Then status 204

    # Get the deleted CSG
    Given header Authorization = token.bearerToken
    And path '/assortment/storeGroup/customStoreGroup/' + groupId
    When method GET
    Then status 404