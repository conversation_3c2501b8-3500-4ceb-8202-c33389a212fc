package com.krogerqa.sfpAppsKarate;

import com.intuit.karate.Results;
import com.intuit.karate.Runner;
import com.krogerqa.karatecentral.utilities.BaseTestRunner;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;


public class SmokeTestRunner extends BaseTestRunner {

  @Test
  public void smokeTestRun() {
    Results results = Runner.path("src/test/java/com/krogerqa/sfpAppsKarate/features")
        .outputJunitXml(true)
        .outputCucumberJson(true)
        .outputHtmlReport(true)
        .tags("@smoke")
        .parallel(1);
    Assertions.assertTrue(results.getFailCount() == 0, results.getErrorMessages());
  }

}
